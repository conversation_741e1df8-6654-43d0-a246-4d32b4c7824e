import React, { useState } from "react";

// --- Types for Question Breakdown ---
interface CriterionBreakdown {
    criterion: string;
    score: string;
    maxScore?: string;
}

interface FeedbackSection {
    title: string;
    content: string;
    subsections?: { title: string; content: string }[];
}

interface QuestionBreakdown {
    questionNumber: string;
    marksAwarded: number;
    marksPossible: number;
    percentage: number;
    feedback: string;
    structuredFeedback?: FeedbackSection[];
    criteriaBreakdown: CriterionBreakdown[];
}

interface EvaluationBreakdown {
    totalMarks: number;
    maxMarks: number;
    overallPercentage: number;
    questions: QuestionBreakdown[];
}

// --- Validation and Sanitization Utilities ---
const sanitizeNumber = (value: any, fallback: number = 0): number => {
    if (typeof value === 'number' && !isNaN(value) && isFinite(value)) {
        return Math.max(0, Math.round(value));
    }
    if (typeof value === 'string') {
        const parsed = parseFloat(value.replace(/[^\d.-]/g, ''));
        if (!isNaN(parsed) && isFinite(parsed)) {
            return Math.max(0, Math.round(parsed));
        }
    }
    return fallback;
};

const sanitizeString = (value: any, fallback: string = ''): string => {
    if (typeof value === 'string') {
        return value.trim();
    }
    if (value != null) {
        return String(value).trim();
    }
    return fallback;
};

const sanitizePercentage = (awarded: number, possible: number): number => {
    if (possible <= 0) return 0;
    const percentage = (awarded / possible) * 100;
    return Math.max(0, Math.min(100, Math.round(percentage)));
};

// --- XML Validation and Repair Utilities ---
const validateXMLStructure = (xmlString: string): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    // Check for basic XML structure
    if (!xmlString.includes('<evaluation>')) {
        errors.push('Missing <evaluation> root element');
    }
    if (!xmlString.includes('</evaluation>')) {
        errors.push('Missing </evaluation> closing tag');
    }

    // Check for required elements (support both old and new formats)
    const totalMarksElements = ['total_marks_awarded', 'total_marks'];
    const maxMarksElements = ['maximum_possible_marks', 'max_marks', 'possible_marks'];

    const hasTotalMarks = totalMarksElements.some(element => xmlString.includes(`<${element}>`));
    const hasMaxMarks = maxMarksElements.some(element => xmlString.includes(`<${element}>`));

    if (!hasTotalMarks) {
        errors.push('Missing total marks element (total_marks_awarded or total_marks)');
    }
    if (!hasMaxMarks) {
        errors.push('Missing maximum marks element (maximum_possible_marks, max_marks, or possible_marks)');
    }

    return {
        isValid: errors.length === 0,
        errors
    };
};

const repairXMLStructure = (xmlString: string): string => {
    let repairedXml = xmlString;

    // Handle escaped quotes and newlines from JSON strings
    repairedXml = repairedXml
        .replace(/\\"/g, '"')
        .replace(/\\n/g, '\n')
        .replace(/\\\\/g, '\\');

    // Remove control characters and normalize whitespace
    repairedXml = repairedXml
        .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')
        .replace(/&(?![a-zA-Z0-9#]{1,6};)/g, '&amp;')
        .trim();

    // Fix common structural issues
    // 1. Ensure proper evaluation wrapper
    if (!repairedXml.startsWith('<evaluation>')) {
        repairedXml = '<evaluation>' + repairedXml;
    }
    if (!repairedXml.endsWith('</evaluation>')) {
        repairedXml = repairedXml + '</evaluation>';
    }

    // 2. Fix unclosed question tags
    repairedXml = repairedXml.replace(/<question([^>]*)>([^<]*(?:<(?!\/question>|question)[^<]*)*)<\/evaluation>/g,
        (_, attrs, content) => `<question${attrs}>${content}</question></evaluation>`);

    // 3. Fix nested question tags
    repairedXml = repairedXml.replace(/<question([^>]*)>([^<]*(?:<(?!question|\/question)[^<]*)*)<question/g,
        (_, attrs, content) => `<question${attrs}>${content}</question><question`);

    // 4. Fix unclosed section tags
    repairedXml = repairedXml.replace(/<section([^>]*)>([^<]*(?:<(?!\/section>)[^<]*)*)<\/evaluation>/g,
        (_, attrs, content) => `<section${attrs}>${content}</section></evaluation>`);

    // 5. Handle malformed criterion tags
    repairedXml = repairedXml.replace(/<criterion([^>]*?)>([^<]*?)<criterion/g,
        (_, attrs, content) => `<criterion${attrs}>${content}</criterion><criterion`);

    return repairedXml;
};

const extractAndValidateXML = (rawData: any): string | null => {
    try {
        let xmlString = '';

        // Handle different input formats with robust validation
        if (Array.isArray(rawData)) {
            if (rawData.length === 0) {
                console.error('Empty array provided for evaluation data');
                return null;
            }
            xmlString = sanitizeString(rawData[0]);
        } else if (typeof rawData === 'string') {
            xmlString = sanitizeString(rawData);
        } else if (rawData && typeof rawData === 'object') {
            // Handle object format (future compatibility)
            if (rawData.evaluation) {
                xmlString = sanitizeString(rawData.evaluation);
            } else if (rawData.content) {
                xmlString = sanitizeString(rawData.content);
            } else {
                console.error('Object format not recognized:', Object.keys(rawData));
                return null;
            }
        } else {
            console.error('Invalid evaluation data format:', typeof rawData, rawData);
            return null;
        }

        if (!xmlString) {
            console.error('Empty XML string after extraction');
            return null;
        }

        // Extract XML content from markdown or raw string
        const xmlMatch = xmlString.match(/<evaluation>([\s\S]*?)<\/evaluation>/);
        if (!xmlMatch) {
            // Try to find evaluation content without proper tags
            const contentMatch = xmlString.match(/total_marks_awarded|maximum_possible_marks|question/);
            if (contentMatch) {
                // Wrap content in evaluation tags
                xmlString = `<evaluation>${xmlString}</evaluation>`;
            } else {
                console.error('No evaluation XML found in data');
                return null;
            }
        } else {
            xmlString = `<evaluation>${xmlMatch[1]}</evaluation>`;
        }

        return repairXMLStructure(xmlString);
    } catch (error) {
        console.error('Error extracting XML:', error);
        return null;
    }
};

// --- Question Breakdown Function ---
export const parseQuestionBreakdown = (evaluationData: any): EvaluationBreakdown | null => {
    try {
        // Extract and validate XML with comprehensive error handling
        const xmlContent = extractAndValidateXML(evaluationData);
        if (!xmlContent) {
            return null;
        }

        // Validate XML structure before parsing
        const validation = validateXMLStructure(xmlContent);
        if (!validation.isValid) {
            console.warn('XML structure issues detected:', validation.errors);
            // Continue with parsing but log warnings
        }

        // Parse XML with comprehensive error handling
        const parser = new DOMParser();
        const xmlDoc = parser.parseFromString(xmlContent, 'text/xml');

        // Check for parser errors
        const parserError = xmlDoc.querySelector('parsererror');
        if (parserError) {
            console.error('XML parsing error:', parserError.textContent);
            console.error('Problematic XML:', xmlContent.substring(0, 500) + '...');

            // Try one more repair attempt for common issues
            const repairedXml = xmlContent
                .replace(/<(\w+)([^>]*?)([^>\/])>/g, '<$1$2$3/>')  // Fix self-closing tags
                .replace(/&(?![a-zA-Z0-9#]{1,6};)/g, '&amp;');     // Fix remaining ampersands

            const retryDoc = parser.parseFromString(repairedXml, 'text/xml');
            const retryError = retryDoc.querySelector('parsererror');
            if (retryError) {
                console.error('XML repair failed, returning null');
                return null;
            }
            // Use repaired document
            return parseEvaluationFromDOM(retryDoc);
        }

        return parseEvaluationFromDOM(xmlDoc);
    } catch (error) {
        console.error('Error parsing evaluation breakdown:', error);
        return null;
    }
};

// --- Structured Feedback Component ---
const StructuredFeedbackDisplay: React.FC<{
    feedback: string;
    structuredFeedback?: FeedbackSection[];
    questionNumber: string;
}> = ({ feedback, structuredFeedback, questionNumber }) => {
    const [isExpanded, setIsExpanded] = useState(false);
    const [expandedSections, setExpandedSections] = useState<Set<number>>(new Set());

    const toggleSection = (index: number) => {
        const newExpanded = new Set(expandedSections);
        if (newExpanded.has(index)) {
            newExpanded.delete(index);
        } else {
            newExpanded.add(index);
        }
        setExpandedSections(newExpanded);
    };

    // If we have structured feedback, use it; otherwise fall back to plain text
    if (structuredFeedback && structuredFeedback.length > 0) {
        return (
            <div className="space-y-4">
                {structuredFeedback.map((section, index) => (
                    <div key={index} className="border border-border rounded-lg overflow-hidden">
                        <button
                            onClick={() => toggleSection(index)}
                            className="w-full p-3 bg-muted/20 hover:bg-muted/30 transition-colors text-left flex items-center justify-between"
                        >
                            <h5 className="font-medium text-foreground">{section.title}</h5>
                            <span className="text-muted-foreground text-sm">
                                {expandedSections.has(index) ? '−' : '+'}
                            </span>
                        </button>

                        {expandedSections.has(index) && (
                            <div className="p-3 bg-card border-t border-border">
                                <p className="text-sm text-foreground mb-3 leading-relaxed">
                                    {section.content}
                                </p>

                                {section.subsections && section.subsections.length > 0 && (
                                    <div className="space-y-3">
                                        {section.subsections.map((subsection, subIndex) => (
                                            <div key={subIndex} className="pl-4 border-l-2 border-primary/20">
                                                <h6 className="font-medium text-sm text-foreground mb-1">
                                                    {subsection.title}
                                                </h6>
                                                <p className="text-sm text-muted-foreground leading-relaxed">
                                                    {subsection.content}
                                                </p>
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </div>
                        )}
                    </div>
                ))}
            </div>
        );
    }

    // Fallback to plain text with expand/collapse for long feedback
    const isLongFeedback = feedback.length > 500;
    const displayText = isLongFeedback && !isExpanded
        ? `${feedback.substring(0, 500)}...`
        : feedback;

    return (
        <div className="space-y-2">
            <div className="p-3 bg-muted/30 border border-border rounded">
                <p className="text-sm text-foreground leading-relaxed break-words whitespace-pre-wrap">
                    {displayText}
                </p>
            </div>

            {isLongFeedback && (
                <button
                    onClick={() => setIsExpanded(!isExpanded)}
                    className="text-xs text-primary hover:underline transition-colors"
                >
                    {isExpanded ? 'Show less' : 'Show full feedback'}
                </button>
            )}
        </div>
    );
};

const parseCriteriaBreakdown = (questionElement: Element): CriterionBreakdown[] => {
    const criteriaBreakdown: CriterionBreakdown[] = [];

    try {
        // Look for marks breakdown in multiple possible locations
        const marksBreakdown = questionElement.querySelector('marks_breakdown, criteria, breakdown');
        if (!marksBreakdown) {
            return criteriaBreakdown;
        }

        // Extract all criterion elements
        const criteria = Array.from(marksBreakdown.querySelectorAll('criterion'));
        const criteriaMap = new Map<string, { score: string; maxScore?: string }>();

        // First pass: collect all criteria
        criteria.forEach(criterion => {
            const name = sanitizeString(criterion.getAttribute('name'));
            const scoreText = sanitizeString(criterion.textContent);

            if (!name || !scoreText) return;

            // Handle "1/3" format (new format from evaluation output)
            if (scoreText.includes('/')) {
                const [score, maxScore] = scoreText.split('/').map(s => s.trim());
                criteriaMap.set(name, { score, maxScore });
            }
            // Handle "Total Possible for X" pattern (old format)
            else if (name.startsWith('Total Possible for ')) {
                const baseName = name.replace('Total Possible for ', '').trim();
                if (criteriaMap.has(baseName)) {
                    criteriaMap.get(baseName)!.maxScore = scoreText;
                } else {
                    criteriaMap.set(baseName, { score: '0', maxScore: scoreText });
                }
            } else {
                // Regular criterion (old format)
                if (criteriaMap.has(name)) {
                    criteriaMap.get(name)!.score = scoreText;
                } else {
                    criteriaMap.set(name, { score: scoreText, maxScore: undefined });
                }
            }
        });

        // Convert map to array, filtering out empty entries
        criteriaMap.forEach((value, key) => {
            if (key && (value.score !== '0' || value.maxScore)) {
                criteriaBreakdown.push({
                    criterion: key,
                    score: value.score,
                    maxScore: value.maxScore || '0'
                });
            }
        });

    } catch (error) {
        console.warn('Error parsing criteria breakdown:', error);
    }

    return criteriaBreakdown;
};

const parseEvaluationFromDOM = (xmlDoc: Document): EvaluationBreakdown | null => {
    try {
        const evaluation = xmlDoc.querySelector('evaluation');
        if (!evaluation) {
            console.error('No evaluation element found in parsed XML');
            return null;
        }

        // Extract overall evaluation data with robust parsing for new format
        const totalMarksElement = evaluation.querySelector('total_marks_awarded, total_marks');
        const maxMarksElement = evaluation.querySelector('maximum_possible_marks, max_marks, possible_marks');
        const percentageElement = evaluation.querySelector('percentage_score, overall_percentage');

        const totalMarks = sanitizeNumber(totalMarksElement?.textContent);
        const maxMarks = sanitizeNumber(maxMarksElement?.textContent);

        // Calculate percentage if not provided or invalid
        let overallPercentage = sanitizeNumber(percentageElement?.textContent);
        if (overallPercentage === 0 && maxMarks > 0) {
            overallPercentage = sanitizePercentage(totalMarks, maxMarks);
        }

        // Extract question details with comprehensive error handling
        const questionElements = evaluation.querySelectorAll('question');
        const questions: QuestionBreakdown[] = [];

        Array.from(questionElements).forEach((question, index) => {
            try {
                // Extract question number with multiple fallback strategies
                let questionNumber = sanitizeString(question.getAttribute('number'));
                if (!questionNumber) {
                    questionNumber = sanitizeString(question.querySelector('number')?.textContent);
                }
                if (!questionNumber) {
                    questionNumber = `Question ${index + 1}`;
                }

                // Extract marks with validation for new format
                const marksAwardedElement = question.querySelector('marks_awarded, awarded, score');
                const marksPossibleElement = question.querySelector('marks_possible, possible_marks, possible, max_marks, total');

                const marksAwarded = sanitizeNumber(marksAwardedElement?.textContent);
                const marksPossible = sanitizeNumber(marksPossibleElement?.textContent);

                // Calculate percentage with validation
                const percentage = sanitizePercentage(marksAwarded, marksPossible);

                // Extract feedback with complex nested structure handling
                let feedback = '';
                let structuredFeedback: FeedbackSection[] = [];

                // Try different feedback selectors
                const feedbackElement = question.querySelector('feedback, detailed_feedback, comment, overall_comment');

                if (feedbackElement) {
                    // Check if it's a simple text element
                    const directText = sanitizeString(feedbackElement.textContent);
                    if (directText && !feedbackElement.querySelector('*')) {
                        feedback = directText;
                    } else {
                        // Handle complex nested structure like detailed_feedback
                        const feedbackParts: string[] = [];

                        // Extract overall comment
                        const overallComment = feedbackElement.querySelector('overall_comment');
                        if (overallComment) {
                            const content = sanitizeString(overallComment.textContent);
                            if (content) {
                                feedbackParts.push(`Overall: ${content}`);
                                structuredFeedback.push({
                                    title: 'Overall Assessment',
                                    content: content
                                });
                            }
                        }

                        // Extract structural analysis
                        const structuralAnalysis = feedbackElement.querySelector('structural_analysis');
                        if (structuralAnalysis) {
                            const structuralParts: string[] = [];
                            const subsections: { title: string; content: string }[] = [];

                            ['introduction', 'body_structure_and_flow', 'conclusion'].forEach(part => {
                                const element = structuralAnalysis.querySelector(part);
                                if (element) {
                                    const text = sanitizeString(element.textContent);
                                    if (text) {
                                        const title = part.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                                        structuralParts.push(`${title}: ${text}`);
                                        subsections.push({ title, content: text });
                                    }
                                }
                            });

                            if (structuralParts.length > 0) {
                                feedbackParts.push(`Structure: ${structuralParts.join('; ')}`);
                                structuredFeedback.push({
                                    title: 'Structural Analysis',
                                    content: 'Analysis of answer structure and organization',
                                    subsections
                                });
                            }
                        }

                        // Extract content analysis
                        const contentAnalysis = feedbackElement.querySelector('content_analysis');
                        if (contentAnalysis) {
                            const contentParts: string[] = [];
                            const subsections: { title: string; content: string }[] = [];

                            ['relevance_and_accuracy', 'analytical_depth_and_rigor', 'keyword_and_citation_usage'].forEach(part => {
                                const element = contentAnalysis.querySelector(part);
                                if (element) {
                                    const text = sanitizeString(element.textContent);
                                    if (text) {
                                        const title = part.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                                        contentParts.push(`${title}: ${text}`);
                                        subsections.push({ title, content: text });
                                    }
                                }
                            });

                            if (contentParts.length > 0) {
                                feedbackParts.push(`Content: ${contentParts.join('; ')}`);
                                structuredFeedback.push({
                                    title: 'Content Analysis',
                                    content: 'Evaluation of content quality and accuracy',
                                    subsections
                                });
                            }
                        }

                        // Extract presentation analysis
                        const presentationAnalysis = feedbackElement.querySelector('presentation_analysis');
                        if (presentationAnalysis) {
                            const presentationParts: string[] = [];
                            const subsections: { title: string; content: string }[] = [];

                            ['clarity_and_language', 'visuals'].forEach(part => {
                                const element = presentationAnalysis.querySelector(part);
                                if (element) {
                                    const text = sanitizeString(element.textContent);
                                    if (text) {
                                        const title = part.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                                        presentationParts.push(`${title}: ${text}`);
                                        subsections.push({ title, content: text });
                                    }
                                }
                            });

                            if (presentationParts.length > 0) {
                                feedbackParts.push(`Presentation: ${presentationParts.join('; ')}`);
                                structuredFeedback.push({
                                    title: 'Presentation Analysis',
                                    content: 'Assessment of clarity, language, and visual elements',
                                    subsections
                                });
                            }
                        }

                        feedback = feedbackParts.join('\n\n');
                    }
                }

                // Extract criteria breakdown with robust parsing
                const criteriaBreakdown = parseCriteriaBreakdown(question);

                // Only add question if it has valid data
                if (questionNumber && (marksAwarded > 0 || marksPossible > 0 || feedback || criteriaBreakdown.length > 0)) {
                    questions.push({
                        questionNumber,
                        marksAwarded,
                        marksPossible,
                        percentage,
                        feedback,
                        structuredFeedback: structuredFeedback.length > 0 ? structuredFeedback : undefined,
                        criteriaBreakdown
                    });
                }
            } catch (error) {
                console.warn(`Error parsing question ${index + 1}:`, error);
                // Continue with other questions
            }
        });

        // Validate final data before returning
        if (questions.length === 0) {
            console.warn('No valid questions found in evaluation data');
        }

        // Ensure totals are consistent with question data
        const calculatedTotal = questions.reduce((sum, q) => sum + q.marksAwarded, 0);
        const calculatedMax = questions.reduce((sum, q) => sum + q.marksPossible, 0);

        // Use calculated values if they seem more reliable
        const finalTotalMarks = totalMarks > 0 ? totalMarks : calculatedTotal;
        const finalMaxMarks = maxMarks > 0 ? maxMarks : calculatedMax;
        const finalPercentage = finalMaxMarks > 0 ? sanitizePercentage(finalTotalMarks, finalMaxMarks) : overallPercentage;

        return {
            totalMarks: finalTotalMarks,
            maxMarks: finalMaxMarks,
            overallPercentage: finalPercentage,
            questions
        };

    } catch (error) {
        console.error('Error parsing evaluation from DOM:', error);
        return null;
    }
};

// --- Display Component for Question Breakdown ---
const QuestionBreakdownDisplay: React.FC<{ evaluationData: EvaluationBreakdown | null }> = ({ evaluationData }) => {
    // Handle null or invalid data
    if (!evaluationData) {
        return (
            <div className="bg-card border border-border rounded-lg p-6 text-center">
                <p className="text-muted-foreground">Unable to load question breakdown data.</p>
                <p className="text-xs text-muted-foreground mt-2">
                    Please check if the evaluation data is properly formatted.
                </p>
            </div>
        );
    }

    // Handle empty questions array
    if (!evaluationData.questions || evaluationData.questions.length === 0) {
        return (
            <div className="bg-card border border-border rounded-lg p-6 text-center">
                <p className="text-muted-foreground">No question breakdown available.</p>
                <p className="text-xs text-muted-foreground mt-2">
                    The evaluation may not contain detailed question-wise analysis.
                </p>
            </div>
        );
    }
    const getPerformanceColor = (percentage: number): string => {
        if (percentage >= 80) return "text-primary";
        if (percentage >= 60) return "text-foreground";
        if (percentage >= 40) return "text-muted-foreground";
        return "text-destructive";
    };

    const getPerformanceBg = (percentage: number): string => {
        if (percentage >= 80) return "bg-primary/5 border-primary/20";
        if (percentage >= 60) return "bg-accent/30 border-border";
        if (percentage >= 40) return "bg-muted/50 border-border";
        return "bg-destructive/5 border-destructive/20";
    };

    const getPerformanceBadge = (percentage: number): string => {
        if (percentage >= 80) return "bg-primary/10 text-primary border-primary/20";
        if (percentage >= 60) return "bg-accent text-foreground border-border";
        if (percentage >= 40) return "bg-muted text-muted-foreground border-border";
        return "bg-destructive/10 text-destructive border-destructive/20";
    };

    return (
        <div className="space-y-4 lg:space-y-6 p-2 lg:p-0">
            {/* Overall Performance */}
            {/* <div className="bg-card border border-border rounded-lg p-6">
                <h2 className="text-lg font-semibold mb-4 text-foreground">Overall Performance</h2>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center p-4 bg-muted/30 rounded-lg">
                        <p className="text-sm text-muted-foreground mb-1">Total Score</p>
                        <p className="text-2xl font-semibold text-foreground">
                            {evaluationData.totalMarks}/{evaluationData.maxMarks}
                        </p>
                    </div>
                    <div className="text-center p-4 bg-muted/30 rounded-lg">
                        <p className="text-sm text-muted-foreground mb-1">Percentage</p>
                        <p className={`text-2xl font-semibold ${getPerformanceColor(evaluationData.overallPercentage)}`}>
                            {evaluationData.overallPercentage}%
                        </p>
                    </div>
                    <div className="text-center p-4 bg-muted/30 rounded-lg">
                        <p className="text-sm text-muted-foreground mb-1">Questions</p>
                        <p className="text-2xl font-semibold text-foreground">{evaluationData.questions.length}</p>
                    </div>
                </div>
            </div> */}

            {/* Individual Question Breakdown */}
            <div className="space-y-3 lg:space-y-4">
                {/* <h2 className="text-lg font-semibold text-foreground">Question-wise Breakdown</h2> */}
                {evaluationData.questions.map((question) => (
                    <div key={question.questionNumber} className="bg-card border border-border rounded-lg overflow-hidden">
                        {/* Question Header */}
                        <div className={`p-3 lg:p-4 border-b ${getPerformanceBg(question.percentage)}`}>
                            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 sm:gap-0">
                                <div className="flex items-center gap-2 lg:gap-3">
                                    <h3 className="text-sm lg:text-base font-semibold text-foreground">
                                        {question.questionNumber}
                                    </h3>
                                    <span className={`px-2 py-1 rounded text-xs font-medium border ${getPerformanceBadge(question.percentage)}`}>
                                        {question.percentage}%
                                    </span>
                                </div>
                                <div className="text-left sm:text-right">
                                    <p className={`text-sm lg:text-base font-semibold ${getPerformanceColor(question.percentage)}`}>
                                        {question.marksAwarded}/{question.marksPossible}
                                    </p>
                                    <p className="text-xs text-muted-foreground">marks</p>
                                </div>
                            </div>
                        </div>

                        {/* Question Content */}
                        <div className="p-3 lg:p-4 space-y-3 lg:space-y-4">
                            {/* Criteria Breakdown */}
                            {question.criteriaBreakdown.length > 0 && (
                                <div>
                                    <h4 className="text-sm font-medium text-foreground mb-2">Marking Criteria</h4>
                                    <div className="space-y-2">
                                        {question.criteriaBreakdown.map((criterion, criterionIndex) => {
                                            // Robust percentage calculation with validation
                                            const score = sanitizeNumber(criterion.score);
                                            const maxScore = sanitizeNumber(criterion.maxScore);
                                            const criterionPercentage = sanitizePercentage(score, maxScore);

                                            // Handle empty or invalid criterion names
                                            const criterionName = sanitizeString(criterion.criterion) || `Criterion ${criterionIndex + 1}`;

                                            return (
                                                <div key={criterionIndex} className="flex flex-col gap-2 p-3 bg-muted/30 rounded border border-border">
                                                    <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2">
                                                        <span className="text-sm text-foreground flex-1 leading-relaxed" title={criterionName}>
                                                            {criterionName}
                                                        </span>
                                                        <div className="flex items-center gap-2 justify-start sm:justify-end flex-shrink-0">
                                                            <span className="text-sm font-medium text-foreground">
                                                                {score}/{maxScore || '?'}
                                                            </span>
                                                            {maxScore > 0 && (
                                                                <span className={`text-xs px-2 py-1 rounded font-medium ${getPerformanceBadge(criterionPercentage)}`}>
                                                                    {criterionPercentage}%
                                                                </span>
                                                            )}
                                                        </div>
                                                    </div>
                                                </div>
                                            );
                                        })}
                                    </div>
                                </div>
                            )}

                            {/* Feedback */}
                            {question.feedback && (
                                <div>
                                    <h4 className="text-sm font-medium text-foreground mb-2">Detailed Feedback</h4>
                                    <StructuredFeedbackDisplay
                                        feedback={question.feedback}
                                        structuredFeedback={question.structuredFeedback}
                                        questionNumber={question.questionNumber}
                                    />
                                </div>
                            )}
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

// --- Error Boundary Component ---
class QuestionBreakdownErrorBoundary extends React.Component<
    { children: React.ReactNode },
    { hasError: boolean; error?: Error }
> {
    constructor(props: { children: React.ReactNode }) {
        super(props);
        this.state = { hasError: false };
    }

    static getDerivedStateFromError(error: Error) {
        return { hasError: true, error };
    }

    componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
        console.error('QuestionBreakdown Error:', error, errorInfo);
    }

    render() {
        if (this.state.hasError) {
            return (
                <div className="bg-card border border-destructive/20 rounded-lg p-6 text-center">
                    <p className="text-destructive font-medium mb-2">Error displaying question breakdown</p>
                    <p className="text-sm text-muted-foreground">
                        There was an issue processing the evaluation data. Please try refreshing the page.
                    </p>
                    <button
                        className="mt-3 px-4 py-2 bg-primary text-primary-foreground rounded text-sm hover:bg-primary/90"
                        onClick={() => this.setState({ hasError: false })}
                    >
                        Try Again
                    </button>
                </div>
            );
        }

        return this.props.children;
    }
}

// --- Main Export with Error Boundary ---
const QuestionBreakdownWithErrorBoundary: React.FC<{ evaluationData: EvaluationBreakdown | null }> = ({ evaluationData }) => {
    return (
        <QuestionBreakdownErrorBoundary>
            <QuestionBreakdownDisplay evaluationData={evaluationData} />
        </QuestionBreakdownErrorBoundary>
    );
};

// --- Utility function for external validation ---
export const validateEvaluationData = (data: any): { isValid: boolean; errors: string[]; warnings: string[] } => {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
        if (!data) {
            errors.push('No evaluation data provided');
            return { isValid: false, errors, warnings };
        }

        const parsed = parseQuestionBreakdown(data);
        if (!parsed) {
            errors.push('Failed to parse evaluation data');
            return { isValid: false, errors, warnings };
        }

        if (parsed.questions.length === 0) {
            warnings.push('No questions found in evaluation data');
        }

        if (parsed.totalMarks === 0 && parsed.maxMarks === 0) {
            warnings.push('No marks information available');
        }

        parsed.questions.forEach((question, index) => {
            if (!question.questionNumber) {
                warnings.push(`Question ${index + 1} has no number`);
            }
            if (question.marksPossible === 0) {
                warnings.push(`Question ${question.questionNumber || index + 1} has no possible marks`);
            }
            if (!question.feedback) {
                warnings.push(`Question ${question.questionNumber || index + 1} has no feedback`);
            }
        });

        return { isValid: true, errors, warnings };
    } catch (error) {
        errors.push(`Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`);
        return { isValid: false, errors, warnings };
    }
};

export default QuestionBreakdownWithErrorBoundary;
