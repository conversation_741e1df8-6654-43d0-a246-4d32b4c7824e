import React, { useMemo, useState } from 'react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { GradingStatus } from '../types/aegisGrader';
import { ToastContainer } from 'react-toastify';
import {
    ArrowLeftIcon,
    ClockIcon,
    UsersIcon,
    ArrowTrendingUpIcon,
    MagnifyingGlassIcon,
} from '@heroicons/react/24/outline';
import {
    CheckCircleIcon as CheckCircleSolid,
} from '@heroicons/react/24/solid';
import { parseQuestionBreakdown } from '@/components/QuestionBreakdown';
import { parseEvaluationForGradingDetails, type ParsedEvaluation } from '@/utils/xmlEvaluationParser';


// --- Type Definitions ---
interface ParsedQuestion {
    number: string;
    marks_awarded: string;
    marks_possible: string;
    feedback: string;
}


interface ParsedSection {
    name: string;
    section_marks: string;
    section_possible_marks: string;
    question: ParsedQuestion[];
}


// ParsedEvaluation is now imported from xmlEvaluationParser


interface EvaluationParser {
    parseEvaluation: (evaluationResult: any) => ParsedEvaluation | null;
}


// --- Configuration for Different Data Formats ---
const EVALUATION_CONFIG: Record<string, EvaluationParser> = {
    // Current format: Array with markdown/XML string
    CURRENT: {
        parseEvaluation: (evaluationResult: any): ParsedEvaluation | null => {
            return parseEvaluationForGradingDetails(evaluationResult);
        }
    },
    // Future format: Direct object (for easy migration)
    FUTURE_OBJECT: {
        parseEvaluation: (evaluationResult: any): ParsedEvaluation | null => {
            if (!evaluationResult?.evaluation) return null;
            return evaluationResult.evaluation;
        }
    }
};

// XML parsing functions moved to xmlEvaluationParser

// Aggressive XML repair for severely malformed XML
const aggressiveXMLRepair = (xmlString: string): string => {
    let repaired = xmlString;

    // Remove any duplicate evaluation tags
    repaired = repaired.replace(/<evaluation><evaluation>/g, '<evaluation>');
    repaired = repaired.replace(/<\/evaluation><\/evaluation>/g, '</evaluation>');

    // Try to fix criterion tags more carefully
    repaired = repaired.replace(/<criterion([^>]*?)>([^<]*?)(?=\n\s*<(?!\/criterion))/g,
        (_, attrs, content) => {
            return `<criterion${attrs}>${content}</criterion>`;
        });

    return repaired;
};

// Extract evaluation data from parsed XML document
const parseEvaluationFromXMLDoc = (xmlDoc: Document): ParsedEvaluation | null => {
    const evaluation = xmlDoc.querySelector('evaluation');
    if (!evaluation) {
        console.error('No evaluation element found in XML');
        return null;
    }

    // Use updated selectors to match new XML format
    const totalMarks = evaluation.querySelector('total_marks_awarded, total_marks')?.textContent || '0';
    const maxMarks = evaluation.querySelector('maximum_possible_marks, max_marks, possible_marks')?.textContent || '0';
    const percentage = evaluation.querySelector('percentage_score, overall_percentage')?.textContent || '0';

    // Handle both section-based and direct question-based formats
    const sections = Array.from(evaluation.querySelectorAll('section')).map((section): ParsedSection => {
        const questions = Array.from(section.querySelectorAll('question')).map((q): ParsedQuestion => ({
            number: q.getAttribute('number') || q.querySelector('number')?.textContent || '0',
            marks_awarded: q.querySelector('marks_awarded, awarded, score')?.textContent || '0',
            marks_possible: q.querySelector('marks_possible, possible_marks, possible, max_marks, total')?.textContent || '0',
            feedback: q.querySelector('feedback, detailed_feedback, comment, overall_comment')?.textContent || ''
        }));

        return {
            name: section.querySelector('name')?.textContent || '',
            section_marks: section.querySelector('section_marks')?.textContent || '0',
            section_possible_marks: section.querySelector('section_possible_marks')?.textContent || '0',
            question: questions
        };
    });

    // If no sections found, try to parse questions directly (new format)
    if (sections.length === 0) {
        const directQuestions = Array.from(evaluation.querySelectorAll('question')).map((q): ParsedQuestion => ({
            number: q.getAttribute('number') || q.querySelector('number')?.textContent || '0',
            marks_awarded: q.querySelector('marks_awarded, awarded, score')?.textContent || '0',
            marks_possible: q.querySelector('marks_possible, possible_marks, possible, max_marks, total')?.textContent || '0',
            feedback: q.querySelector('feedback, detailed_feedback, comment, overall_comment')?.textContent || ''
        }));

        if (directQuestions.length > 0) {
            sections.push({
                name: 'Main Section',
                section_marks: totalMarks,
                section_possible_marks: maxMarks,
                question: directQuestions
            });
        }
    }

    return {
        total_marks: totalMarks,
        maximum_possible_marks: maxMarks,
        percentage_score: percentage,
        section: sections
    };
};

// XML Parser with robust error handling and tag mismatch fixes
const parseXMLEvaluation = (xmlString: string): ParsedEvaluation | null => {
    try {
        // Clean the XML string to remove any malformed characters and fix common issues
        let cleanedXmlString = xmlString
            .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') // Remove control characters
            .replace(/&(?![a-zA-Z0-9#]{1,6};)/g, '&amp;') // Escape unescaped ampersands
            .trim();

        // Ensure proper evaluation wrapper
        if (!cleanedXmlString.startsWith('<evaluation>')) {
            cleanedXmlString = '<evaluation>' + cleanedXmlString;
        }
        if (!cleanedXmlString.endsWith('</evaluation>')) {
            cleanedXmlString = cleanedXmlString + '</evaluation>';
        }

        // Advanced XML structure repair
        cleanedXmlString = repairXMLStructure(cleanedXmlString);

        const parser = new DOMParser();
        const xmlDoc = parser.parseFromString(cleanedXmlString, 'text/xml');


        // Check for parser errors
        const parserError = xmlDoc.querySelector('parsererror');
        if (parserError) {
            console.error('XML parsing error:', parserError.textContent);
            console.error('Problematic XML:', cleanedXmlString.substring(0, 500) + '...');

            // Try one more aggressive repair attempt
            const aggressivelyRepairedXml = aggressiveXMLRepair(cleanedXmlString);
            if (aggressivelyRepairedXml !== cleanedXmlString) {
                console.log('Attempting aggressive XML repair...');
                const retryDoc = parser.parseFromString(aggressivelyRepairedXml, 'text/xml');
                const retryError = retryDoc.querySelector('parsererror');
                if (!retryError) {
                    console.log('Aggressive repair successful');
                    return parseEvaluationFromXMLDoc(retryDoc);
                }
            }

            return null;
        }


        // Use the helper function to parse the evaluation
        return parseEvaluationFromXMLDoc(xmlDoc);
    } catch (error) {
        console.error('Error parsing XML evaluation:', error);
        return null;
    }
};


// --- Constants ---
export const CONSTANTS = {
    TOAST_DELAY: 2000,
    DOWNLOAD_DELAY: 1000,
    // Change this to switch between formats easily
    CURRENT_FORMAT: 'CURRENT' as keyof typeof EVALUATION_CONFIG
};

// Export for testing
export { EVALUATION_CONFIG };


// --- Type Definitions (Simplified) ---
interface GradingResult {
    questionNumber: number;
    maxMarks: number;
    marksAwarded: number;
    feedback: string;
}


interface AnswerSheetResult {
    id: string;
    studentName: string;
    rollNumber: string;
    totalMarks: number;
    maxMarks: number;
    percentage: number;
    results: GradingResult[];
    detailedBreakdown: any;
    pdfUrl?: string;
}


interface AnswerSheetData {
    id: string;
    studentName: string;
    rollNumber: string;
    pdfUrl?: string;
    evaluationResult?: any;
}


interface SubmissionData {
    id: string;
    status: GradingStatus | string;
    testDetails: {
        subject: string;
        className: string;
        date: string;
    };
    questionPaper?: { pdfUrl?: string };
    rubric?: { pdfUrl?: string };
    gradingProgress?: number;
    answerSheets: AnswerSheetData[];
}


// --- Helper Functions (Using Theme Classes) ---
const formatScore = (score: number | string | undefined): string => {
    const num = typeof score === 'string' ? parseFloat(score) : score;
    if (num === undefined || isNaN(num)) return '-';
    return num % 1 === 0 ? num.toString() : num.toFixed(1);
};


const getScoreColorClass = (percentage: number | undefined): string => {
    if (percentage === undefined || isNaN(percentage)) return "text-gray-500";
    if (percentage >= 80) return "text-success-600 dark:text-success-400";
    if (percentage >= 60) return "text-primary-600 dark:text-primary-400";
    if (percentage >= 40) return "text-warning-600 dark:text-warning-400";
    return "text-danger-600 dark:text-danger-400";
};


const getScoreBgClass = (percentage: number | undefined): string => {
    if (percentage === undefined || isNaN(percentage)) return "bg-gray-100 dark:bg-gray-800";
    if (percentage >= 80) return "bg-success-100 dark:bg-success-900/20";
    if (percentage >= 60) return "bg-primary-100 dark:bg-primary-900/20";
    if (percentage >= 40) return "bg-warning-100 dark:bg-warning-900/20";
    return "bg-danger-100 dark:bg-danger-900/20";
};




// --- Status Badge ---
const StatusBadge: React.FC<{ status: GradingStatus | string }> = ({ status }) => {
    const isCompleted = status === GradingStatus.COMPLETED || status === 'COMPLETED';
    const isInProgress = status === GradingStatus.IN_PROGRESS || status === 'IN_PROGRESS';
    const isPending = status === GradingStatus.PENDING || status === 'PENDING';


    let classes = '';
    let icon = null;
    let displayStatus = status;


    if (isCompleted) {
        classes = 'bg-success-100 text-success-800 dark:bg-success-900/20 dark:text-success-400';
        icon = <CheckCircleSolid className="h-4 w-4 inline mr-1" />;
        displayStatus = 'COMPLETED';
    } else if (isInProgress) {
        classes = 'bg-primary-100 text-primary-800 dark:bg-primary-900/20 dark:text-primary-400';
        icon = <ClockIcon className="h-4 w-4 inline mr-1" />;
        displayStatus = 'IN PROGRESS';
    } else if (isPending) {
        classes = 'bg-warning-100 text-warning-800 dark:bg-warning-900/20 dark:text-warning-400';
        icon = <ClockIcon className="h-4 w-4 inline mr-1" />;
        displayStatus = 'PENDING';
    } else {
        classes = 'bg-primary-100 text-primary-800 dark:bg-primary-900/20 dark:text-primary-400';
        icon = <ClockIcon className="h-4 w-4 inline mr-1" />;
    }


    return (
        <div className={`px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-medium ${classes}`}>
            {icon}
            <span className="hidden xs:inline sm:inline">{displayStatus}</span>
            <span className="xs:hidden sm:hidden">{displayStatus.split(' ')[0]}</span>
        </div>
    );
};


// --- Student Card ---
const StudentCard: React.FC<{
    sheet: AnswerSheetData;
    onViewResults: (result: AnswerSheetResult) => void;
    formatResults: (evaluationResult: any) => AnswerSheetResult | null;
}> = ({ sheet, onViewResults, formatResults }) => {
    const parsedEvaluation = useMemo(() => {
        return EVALUATION_CONFIG[CONSTANTS.CURRENT_FORMAT].parseEvaluation(sheet.evaluationResult);
    }, [sheet.evaluationResult]);


    const totalMarks = parsedEvaluation ? parseFloat(parsedEvaluation.total_marks) : undefined;
    const maxMarks = parsedEvaluation ? parseFloat(parsedEvaluation.maximum_possible_marks) : undefined;

    // Calculate percentage if not provided or if it's 0
    let percentage = parsedEvaluation ? parseFloat(parsedEvaluation.percentage_score) : undefined;
    if ((!percentage || percentage === 0) && totalMarks !== undefined && maxMarks !== undefined && maxMarks > 0) {
        percentage = Math.round((totalMarks / maxMarks) * 100);
    }

    return (
        <div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4 p-3 sm:p-4 border border-border dark:border-border rounded-lg hover:shadow-sm dark:hover:shadow-lg hover:border-primary/20 dark:hover:border-primary/30 transition-all duration-200">
            <div className="flex items-center gap-3 flex-1 min-w-0">
                <div className={`w-10 h-10 sm:w-12 sm:h-12 rounded-full ${getScoreBgClass(percentage)} flex items-center justify-center font-medium text-xs sm:text-sm flex-shrink-0`}>
                    {sheet.studentName?.split(' ').map(n => n[0]).join('').toUpperCase() || '?'}
                </div>
                <div className="min-w-0 flex-1">
                    <p className="font-medium text-foreground text-sm sm:text-base truncate">{sheet.studentName || "N/A"}</p>
                    <p className="text-xs sm:text-sm text-muted-foreground">Roll: {sheet.rollNumber || "N/A"}</p>
                </div>
            </div>


            <div className="flex items-center justify-between sm:justify-end gap-3 sm:gap-4">
                {parsedEvaluation ? (
                    <div className="text-left sm:text-right">
                        <p className={`font-semibold text-sm sm:text-base ${getScoreColorClass(percentage)}`}>
                            {formatScore(totalMarks)}/{formatScore(maxMarks)}
                        </p>
                        <p className={`text-xs sm:text-sm ${getScoreColorClass(percentage)}`}>
                            {formatScore(percentage)}%
                        </p>
                    </div>
                ) : (
                    <div className="text-left sm:text-right">
                        <p className="text-muted-foreground text-sm">Not Graded</p>
                    </div>
                )}


                {parsedEvaluation && (
                    <button
                        className="px-3 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors text-xs sm:text-sm whitespace-nowrap flex-shrink-0 min-h-[36px] sm:min-h-[40px]"
                        onClick={() => {
                            const result = formatResults(sheet.evaluationResult);
                            if (result) onViewResults(result);
                        }}
                    >
                        <span className="hidden sm:inline">View Details</span>
                        <span className="sm:hidden">Details</span>
                    </button>
                )}
            </div>
        </div>
    );
};


// --- Main Component ---
export const GradingDetails: React.FC = () => {
    const location = useLocation();
    const navigate = useNavigate();
    const { id: submissionId } = useParams<{ id: string }>();
    const [searchTerm, setSearchTerm] = useState('');


    // Handle navigation to question breakdown page
    const handleViewResults = (result: AnswerSheetResult) => {
        navigate(`/question-breakdown/${result.id}`, {
            state: { submissionData: result }
        });
    };


    const submission = useMemo(() => {
        const history = location.state?.testHistory as SubmissionData[] | undefined;
        return history?.find((sub: SubmissionData) => sub.id === submissionId);
    }, [location.state?.testHistory, submissionId]);


    // --- Usage in your existing component ---
    const enhancedFormatResults = useMemo(() => (evaluationResult: any): AnswerSheetResult | null => {
        const parsedEvaluation = EVALUATION_CONFIG[CONSTANTS.CURRENT_FORMAT].parseEvaluation(evaluationResult);
        if (!parsedEvaluation) return null;


        // Parse the detailed breakdown
        const detailedBreakdown = parseQuestionBreakdown(evaluationResult);


        const sections = Array.isArray(parsedEvaluation.section) ? parsedEvaluation.section : [parsedEvaluation.section];
        const questions: GradingResult[] = sections.flatMap((section: ParsedSection) => {
            const questionsInSection = Array.isArray(section.question) ? section.question : [section.question];
            return questionsInSection.map((q: ParsedQuestion) => ({
                questionNumber: parseFloat(q.number) || 0,
                maxMarks: parseFloat(q.marks_possible) || 0,
                marksAwarded: parseFloat(q.marks_awarded) || 0,
                feedback: q.feedback || ''
            }));
        });


        questions.sort((a, b) => a.questionNumber - b.questionNumber);


        const sheet = submission?.answerSheets.find(s => s.evaluationResult === evaluationResult);


        const totalMarks = parseFloat(parsedEvaluation.total_marks) || 0;
        const maxMarks = parseFloat(parsedEvaluation.maximum_possible_marks) || 0;
        let percentage = parseFloat(parsedEvaluation.percentage_score) || 0;

        // Calculate percentage if not provided or if it's 0
        if (percentage === 0 && maxMarks > 0) {
            percentage = Math.round((totalMarks / maxMarks) * 100);
        }

        return {
            id: sheet?.id || submissionId || '',
            studentName: sheet?.studentName || 'Unknown Student',
            rollNumber: sheet?.rollNumber || 'N/A',
            totalMarks,
            maxMarks,
            percentage,
            results: questions,
            detailedBreakdown,
            pdfUrl: sheet?.pdfUrl
        };
    }, [submission?.answerSheets, submissionId]);


    const filteredSheets = useMemo(() => {
        if (!submission?.answerSheets) return [];
        return submission.answerSheets.filter(sheet =>
            (sheet.studentName?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
            (sheet.rollNumber?.toLowerCase() || '').includes(searchTerm.toLowerCase())
        );
    }, [submission?.answerSheets, searchTerm]);


    const classStats = useMemo(() => {
        if (!submission) return null;


        const gradedSheets = submission.answerSheets?.filter(sheet => {
            const parsed = EVALUATION_CONFIG[CONSTANTS.CURRENT_FORMAT].parseEvaluation(sheet.evaluationResult);
            return parsed !== null;
        });


        if (!gradedSheets || gradedSheets.length === 0) return null;


        const scores = gradedSheets.map(sheet => {
            const parsed = EVALUATION_CONFIG[CONSTANTS.CURRENT_FORMAT].parseEvaluation(sheet.evaluationResult);
            if (!parsed) return 0;

            const totalMarks = parseFloat(parsed.total_marks) || 0;
            const maxMarks = parseFloat(parsed.maximum_possible_marks) || 0;
            let percentage = parseFloat(parsed.percentage_score) || 0;

            // Calculate percentage if not provided or if it's 0
            if (percentage === 0 && maxMarks > 0) {
                percentage = Math.round((totalMarks / maxMarks) * 100);
            }

            return percentage;
        });


        const average = scores.reduce((sum, score) => sum + score, 0) / scores.length;
        const highest = Math.max(...scores);
        const lowest = Math.min(...scores);


        return {
            average,
            highest,
            lowest,
            totalStudents: gradedSheets.length,
            totalSubmissions: submission.answerSheets.length
        };
    }, [submission]);


    if (!submission) {
        return (
            <div className="flex items-center justify-center min-h-screen bg-background px-4">
                <div className="text-center">
                    <p className="text-muted-foreground mb-4">Submission not found</p>
                    <button onClick={() => navigate(-1)} className="text-primary hover:underline">
                        Go Back
                    </button>
                </div>
            </div>
        );
    }


    return (
        <div className="min-h-screen bg-background p-2 sm:p-4 pb-16">
            <ToastContainer position="top-right" autoClose={CONSTANTS.TOAST_DELAY} />


            <div className="space-y-3 sm:space-y-4 pt-2">
                {/* Header */}
                <div className="flex items-center justify-between gap-2 sm:gap-4">
                    <button
                        onClick={() => navigate(-1)}
                        className="flex items-center gap-1 sm:gap-2 p-2 rounded-md text-muted-foreground hover:bg-muted transition-all duration-200 min-h-[40px]"
                    >
                        <ArrowLeftIcon className="w-4 h-4 flex-shrink-0" />
                        <span className="text-sm font-medium">Back</span>
                    </button>
                    <StatusBadge status={submission.status} />
                </div>


                {/* Test Info */}
                <div className="bg-card dark:bg-card rounded-lg shadow-sm border border-border p-4 sm:p-6">
                    <h1 className="text-lg sm:text-xl lg:text-2xl font-bold font-['Space_Grotesk'] mb-2 text-foreground break-words">
                        {submission.testDetails.subject || 'Unnamed Test'}
                    </h1>
                    <p className="text-sm sm:text-base text-muted-foreground break-words">
                        {submission.testDetails.className} • {submission.testDetails.date}
                    </p>
                </div>


                <div className="grid grid-cols-1 lg:grid-cols-4 gap-3 sm:gap-4">
                    {/* Student List */}
                    <div className="lg:col-span-3 order-1 lg:order-1">
                        <div className="bg-card dark:bg-card rounded-lg shadow-sm border border-border overflow-hidden">
                            <div className="p-3 sm:p-4 border-b border-border">
                                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4">
                                    <h2 className="text-base sm:text-lg font-semibold flex items-center gap-2 text-foreground">
                                        <UsersIcon className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
                                        Students ({filteredSheets.length})
                                    </h2>
                                    <div className="relative w-full sm:w-auto sm:min-w-[240px]">
                                        <MagnifyingGlassIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                        <input
                                            type="text"
                                            placeholder="Search students..."
                                            className="w-full pl-10 pr-4 py-2 sm:py-2.5 border border-border rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:border-primary focus:outline-none text-sm sm:text-base"
                                            value={searchTerm}
                                            onChange={(e) => setSearchTerm(e.target.value)}
                                        />
                                    </div>
                                </div>
                            </div>


                            <div className="p-3 sm:p-4 space-y-2 sm:space-y-3">
                                {filteredSheets.length === 0 ? (
                                    <div className="text-center py-8 text-muted-foreground">
                                        No students found
                                    </div>
                                ) : (
                                    filteredSheets.map((sheet) => (
                                        <StudentCard
                                            key={sheet.id}
                                            sheet={sheet}
                                            onViewResults={handleViewResults}
                                            formatResults={enhancedFormatResults}
                                        />
                                    ))
                                )}
                            </div>
                        </div>
                    </div>


                    {/* Stats Sidebar */}
                    <div className="space-y-4 sm:space-y-6 order-2 lg:order-2">
                        {classStats && (
                            <div className="bg-card dark:bg-card rounded-lg shadow-sm border border-border p-3 sm:p-4">
                                <h3 className="font-semibold mb-3 flex items-center gap-2 text-foreground text-sm sm:text-base">
                                    <ArrowTrendingUpIcon className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
                                    Class Stats
                                </h3>
                                <div className="grid grid-cols-2 sm:grid-cols-1 gap-3">
                                    <div>
                                        <p className="text-xs sm:text-sm text-muted-foreground">Average</p>
                                        <p className="font-semibold text-foreground text-sm sm:text-base">{classStats.average.toFixed(1)}%</p>
                                    </div>
                                    <div>
                                        <p className="text-xs sm:text-sm text-muted-foreground">Highest</p>
                                        <p className="font-semibold text-success-600 dark:text-success-400 text-sm sm:text-base">{classStats.highest.toFixed(1)}%</p>
                                    </div>
                                    <div>
                                        <p className="text-xs sm:text-sm text-muted-foreground">Lowest</p>
                                        <p className="font-semibold text-danger-600 dark:text-danger-400 text-sm sm:text-base">{classStats.lowest.toFixed(1)}%</p>
                                    </div>
                                    <div>
                                        <p className="text-xs sm:text-sm text-muted-foreground">Graded</p>
                                        <p className="font-semibold text-foreground text-sm sm:text-base">{classStats.totalStudents}/{classStats.totalSubmissions}</p>
                                    </div>
                                </div>
                            </div>
                        )}


                        <div className="bg-card dark:bg-card rounded-lg shadow-sm border border-border p-3 sm:p-4">
                            <h3 className="font-semibold mb-3 text-foreground text-sm sm:text-base">Actions</h3>
                            <div className="space-y-2 sm:space-y-3">
                                <button
                                    className="w-full px-4 py-2.5 sm:py-3 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-sm sm:text-base font-medium"
                                    disabled={!classStats}
                                >
                                    Download Results
                                </button>
                                <button
                                    className="w-full px-4 py-2.5 sm:py-3 border border-border rounded-md hover:bg-accent hover:text-accent-foreground transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-sm sm:text-base font-medium"
                                    disabled={!classStats}
                                >
                                    Share Results
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};


export default GradingDetails;
